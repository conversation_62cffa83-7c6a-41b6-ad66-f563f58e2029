import { modalTypes } from '@/components/modals/modal-types';
import { openModal } from '@/store/reducer/modal-reducer';
import { Text, useColorModeValue, Flex, Button } from '@chakra-ui/react';
import { useDispatch } from 'react-redux';
import { ChartProp } from '../utils/interface';

function ViewDetails(props: ChartProp) {
   const { kpiDetails, anomaly } = props;
   const dispatch = useDispatch();
   const linkColor = useColorModeValue('#337CDF', '#63B3ED');
   if (!kpiDetails?.current_allData?.length) return null;

   const handleViewOpen = () => {
      dispatch(
         openModal({
            modalType: modalTypes.KPI_VIEW_DETAILS,
            modalProps: { kpiDetails, anomaly },
         }),
      );
   };

   const handleDeepDive = () => {
      // Handler will be implemented by user
      console.log('Deep Dive clicked');
   };

   // Conditional logic for showing Deep Dive button
   const isAnomalyDefined = anomaly !== undefined && anomaly !== null;
   const isSupportedCategory =
      kpiDetails.category === 'facebookads' ||
      kpiDetails.category === 'googleads';
   const showRootCause = isAnomalyDefined && isSupportedCategory;

   // Determine button color based on anomaly
   const buttonColorScheme = anomaly === true ? 'green' : 'red';

   return (
      <Flex justify="space-between" align="center" width="100%">
         <Text
            cursor={'pointer'}
            onClick={handleViewOpen}
            fontSize={14}
            textDecoration={'underline'}
            textAlign={'left'}
            color={linkColor}
         >
            More Details
         </Text>

         {showRootCause && (
            <Button
               size="xs"
               colorScheme={buttonColorScheme}
               variant="solid"
               onClick={handleDeepDive}
               fontSize={12}
               px={3}
               py={1}
               height="auto"
               minHeight="24px"
            >
               Deep Dive
            </Button>
         )}
      </Flex>
   );
}

export default ViewDetails;
